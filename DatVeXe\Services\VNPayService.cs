using DatVeXe.Models;
using Microsoft.Extensions.Configuration;
using System.Globalization;

namespace DatVeXe.Services
{
    /// <summary>
    /// VNPay Service chuẩn - Tích hợp theo tài liệu ch<PERSON>h thức VNPay
    /// </summary>
    public interface IVNPayService
    {
        Task<VNPayPaymentResult> CreatePaymentAsync(VNPayPaymentRequest request);
        Task<VNPayCallbackResult> ProcessCallbackAsync(IQueryCollection queryParams);
        VNPayConfig GetConfig();
        string GetResponseCodeMessage(string responseCode);
    }

    public class VNPayService : IVNPayService
    {
        private readonly VNPayHelper _vnPayHelper;
        private readonly IConfiguration _configuration;
        private readonly ILogger<VNPayService> _logger;
        private readonly VNPayConfig _config;

        public VNPayService(VNPayHelper vnPayHelper, IConfiguration configuration, ILogger<VNPayService> logger)
        {
            _vnPayHelper = vnPayHelper;
            _configuration = configuration;
            _logger = logger;
            _config = new VNPayConfig(configuration);
        }

        public async Task<VNPayPaymentResult> CreatePaymentAsync(VNPayPaymentRequest request)
        {
            try
            {
                _logger.LogInformation($"Creating VNPay payment for TxnRef: {request.TxnRef}, Amount: {request.Amount}");

                // Validate request
                var validationResult = ValidatePaymentRequest(request);
                if (!validationResult.IsValid)
                {
                    return VNPayPaymentResult.CreateFailed(validationResult.ErrorMessage);
                }

                // Tạo VNPay request với thông tin đầy đủ
                var ipAddress = request.IpAddress;
                if (string.IsNullOrEmpty(ipAddress) || ipAddress == "::1")
                {
                    ipAddress = "127.0.0.1"; // VNPay yêu cầu IPv4
                }

                var vnpayRequest = new VNPayRequest
                {
                    TxnRef = request.TxnRef,
                    Amount = request.Amount,
                    OrderInfo = !string.IsNullOrEmpty(request.OrderInfo) ? request.OrderInfo : $"Thanh toan ve xe {request.TxnRef}",
                    OrderType = !string.IsNullOrEmpty(request.OrderType) ? request.OrderType : "other",
                    ReturnUrl = !string.IsNullOrEmpty(request.ReturnUrl) ? request.ReturnUrl : _config.ReturnUrl,
                    IpAddress = ipAddress,
                    CreateDate = DateTime.Now,
                    ExpireDate = DateTime.Now.AddMinutes(_config.ExpireMinutes),
                    BankCode = request.BankCode
                };

                // Tạo payment URL
                var paymentUrl = _vnPayHelper.CreatePaymentUrl(vnpayRequest);

                _logger.LogInformation($"VNPay payment URL created successfully for TxnRef: {request.TxnRef}");
                _logger.LogInformation($"VNPay URL: {paymentUrl}");

                return VNPayPaymentResult.CreateSuccess(paymentUrl, request.TxnRef);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error creating VNPay payment for TxnRef: {request.TxnRef}");
                return VNPayPaymentResult.CreateFailed($"Lỗi tạo thanh toán: {ex.Message}");
            }
        }

        public async Task<VNPayCallbackResult> ProcessCallbackAsync(IQueryCollection queryParams)
        {
            try
            {
                _logger.LogInformation("Processing VNPay callback");

                // Log callback parameters (không log sensitive data)
                var logParams = queryParams.Where(p => !p.Key.Contains("SecureHash"))
                    .ToDictionary(p => p.Key, p => p.Value.ToString());
                _logger.LogInformation($"VNPay callback params: {string.Join(", ", logParams.Select(p => $"{p.Key}={p.Value}"))}");

                // Xử lý callback qua VNPayHelper
                var vnpayResponse = _vnPayHelper.ProcessCallback(queryParams);

                var result = new VNPayCallbackResult
                {
                    Success = vnpayResponse.Success,
                    ResponseCode = vnpayResponse.VnPayResponseCode,
                    TransactionId = vnpayResponse.TransactionId,
                    OrderId = vnpayResponse.OrderId,
                    Amount = vnpayResponse.Amount,
                    BankCode = vnpayResponse.BankCode,
                    BankTranNo = vnpayResponse.BankTranNo,
                    CardType = vnpayResponse.CardType,
                    PayDate = vnpayResponse.PayDate,
                    IsValidSignature = vnpayResponse.IsValidSignature,
                    Message = GetResponseCodeMessage(vnpayResponse.VnPayResponseCode)
                };

                _logger.LogInformation($"VNPay callback processed: Success={result.Success}, ResponseCode={result.ResponseCode}, TransactionId={result.TransactionId}");

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing VNPay callback");
                return new VNPayCallbackResult
                {
                    Success = false,
                    ResponseCode = "99",
                    Message = $"Lỗi xử lý callback: {ex.Message}"
                };
            }
        }

        public VNPayConfig GetConfig()
        {
            return _config;
        }

        public string GetResponseCodeMessage(string responseCode)
        {
            return responseCode switch
            {
                "00" => "Giao dịch thành công",
                "07" => "Trừ tiền thành công. Giao dịch bị nghi ngờ (liên quan tới lừa đảo, giao dịch bất thường).",
                "09" => "Giao dịch không thành công do: Thẻ/Tài khoản của khách hàng chưa đăng ký dịch vụ InternetBanking tại ngân hàng.",
                "10" => "Giao dịch không thành công do: Khách hàng xác thực thông tin thẻ/tài khoản không đúng quá 3 lần",
                "11" => "Giao dịch không thành công do: Đã hết hạn chờ thanh toán. Xin quý khách vui lòng thực hiện lại giao dịch.",
                "12" => "Giao dịch không thành công do: Thẻ/Tài khoản của khách hàng bị khóa.",
                "13" => "Giao dịch không thành công do Quý khách nhập sai mật khẩu xác thực giao dịch (OTP).",
                "24" => "Giao dịch không thành công do: Khách hàng hủy giao dịch",
                "51" => "Giao dịch không thành công do: Tài khoản của quý khách không đủ số dư để thực hiện giao dịch.",
                "65" => "Giao dịch không thành công do: Tài khoản của Quý khách đã vượt quá hạn mức giao dịch trong ngày.",
                "75" => "Ngân hàng thanh toán đang bảo trì.",
                "79" => "Giao dịch không thành công do: KH nhập sai mật khẩu thanh toán quá số lần quy định.",
                "99" => "Các lỗi khác (lỗi còn lại, không có trong danh sách mã lỗi đã liệt kê)",
                _ => $"Mã lỗi không xác định: {responseCode}"
            };
        }

        private ValidationResult ValidatePaymentRequest(VNPayPaymentRequest request)
        {
            if (string.IsNullOrEmpty(request.TxnRef))
                return ValidationResult.Invalid("TxnRef không được để trống");

            if (request.Amount <= 0)
                return ValidationResult.Invalid("Số tiền phải lớn hơn 0");

            if (request.Amount > 1000000000) // 1 tỷ VNĐ
                return ValidationResult.Invalid("Số tiền vượt quá giới hạn cho phép");

            if (string.IsNullOrEmpty(request.OrderInfo))
                return ValidationResult.Invalid("Thông tin đơn hàng không được để trống");

            if (string.IsNullOrEmpty(request.ReturnUrl))
                return ValidationResult.Invalid("Return URL không được để trống");

            if (string.IsNullOrEmpty(request.IpAddress))
                return ValidationResult.Invalid("IP Address không được để trống");

            return ValidationResult.Valid();
        }
    }

    /// <summary>
    /// VNPay Configuration
    /// </summary>
    public class VNPayConfig
    {
        public string TmnCode { get; }
        public string HashSecret { get; }
        public string PaymentUrl { get; }
        public string ReturnUrl { get; }
        public string Version { get; }
        public string Command { get; }
        public string CurrCode { get; }
        public string Locale { get; }
        public int ExpireMinutes { get; }

        public VNPayConfig(IConfiguration configuration)
        {
            TmnCode = configuration["VNPay:TmnCode"] ?? throw new ArgumentNullException("VNPay:TmnCode");
            HashSecret = configuration["VNPay:HashSecret"] ?? throw new ArgumentNullException("VNPay:HashSecret");
            PaymentUrl = configuration["VNPay:PaymentUrl"] ?? throw new ArgumentNullException("VNPay:PaymentUrl");
            ReturnUrl = configuration["VNPay:ReturnUrl"] ?? throw new ArgumentNullException("VNPay:ReturnUrl");
            Version = configuration["VNPay:Version"] ?? "2.1.0";
            Command = configuration["VNPay:Command"] ?? "pay";
            CurrCode = configuration["VNPay:CurrCode"] ?? "VND";
            Locale = configuration["VNPay:Locale"] ?? "vn";
            ExpireMinutes = configuration.GetValue<int>("VNPay:ExpireMinutes", 15);
        }
    }

    /// <summary>
    /// VNPay Payment Request
    /// </summary>
    public class VNPayPaymentRequest
    {
        public string TxnRef { get; set; } = string.Empty;
        public decimal Amount { get; set; }
        public string OrderInfo { get; set; } = string.Empty;
        public string? OrderType { get; set; }
        public string ReturnUrl { get; set; } = string.Empty;
        public string IpAddress { get; set; } = string.Empty;
        public string? BankCode { get; set; }
    }

    /// <summary>
    /// VNPay Payment Result
    /// </summary>
    public class VNPayPaymentResult
    {
        public bool Success { get; set; }
        public string? PaymentUrl { get; set; }
        public string? TransactionId { get; set; }
        public string Message { get; set; } = string.Empty;

        public static VNPayPaymentResult CreateSuccess(string paymentUrl, string transactionId)
        {
            return new VNPayPaymentResult
            {
                Success = true,
                PaymentUrl = paymentUrl,
                TransactionId = transactionId,
                Message = "Tạo thanh toán thành công"
            };
        }

        public static VNPayPaymentResult CreateFailed(string message)
        {
            return new VNPayPaymentResult
            {
                Success = false,
                Message = message
            };
        }
    }

    /// <summary>
    /// VNPay Callback Result
    /// </summary>
    public class VNPayCallbackResult
    {
        public bool Success { get; set; }
        public string ResponseCode { get; set; } = string.Empty;
        public string TransactionId { get; set; } = string.Empty;
        public string OrderId { get; set; } = string.Empty;
        public decimal Amount { get; set; }
        public string BankCode { get; set; } = string.Empty;
        public string BankTranNo { get; set; } = string.Empty;
        public string CardType { get; set; } = string.Empty;
        public string PayDate { get; set; } = string.Empty;
        public bool IsValidSignature { get; set; }
        public string Message { get; set; } = string.Empty;
    }

    /// <summary>
    /// Validation Result
    /// </summary>
    public class ValidationResult
    {
        public bool IsValid { get; set; }
        public string ErrorMessage { get; set; } = string.Empty;

        public static ValidationResult Valid() => new() { IsValid = true };
        public static ValidationResult Invalid(string message) => new() { IsValid = false, ErrorMessage = message };
    }
}
