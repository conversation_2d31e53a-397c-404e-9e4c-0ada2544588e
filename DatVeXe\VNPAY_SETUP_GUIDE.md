# Hướng dẫn tích hợp VNPay với ngrok

## Bước 1: C<PERSON>i đặt và cấu hình ngrok

1. **T<PERSON>i và cài đặt ngrok:**
   - Truy cập https://ngrok.com/
   - Tạo tài khoản miễn phí
   - Tải ngrok cho Windows
   - Gi<PERSON>i nén và đặt ngrok.exe vào thư mục dễ truy cập

2. **Cấu hình ngrok:**
   ```bash
   # Đăng nhập với authtoken (lấy từ dashboard ngrok)
   ngrok authtoken YOUR_AUTH_TOKEN
   
   # Chạy ngrok để expose port 5057
   ngrok http 5057
   ```

3. **Lấy URL công khai:**
   - <PERSON>u khi chạy ngrok, bạn sẽ thấy URL như: `https://abc123.ngrok.io`
   - Copy URL này

## Bước 2: Cập nhật cấu hình

1. **Cập nhật appsettings.Development.json:**
   - <PERSON><PERSON> thế `https://your-ngrok-url.ngrok.io` bằng URL ngrok thật
   - Ví dụ: `https://abc123.ngrok.io`

2. **Cấu hình VNPay Merchant:**
   - Đăng nhập vào VNPay Merchant Admin: https://sandbox.vnpayment.vn/merchantv2/
   - Tài khoản: <EMAIL>
   - Cập nhật Return URL thành: `https://abc123.ngrok.io/Booking/PaymentCallback`

## Bước 3: Test thanh toán

1. **Chạy ứng dụng:**
   ```bash
   dotnet run
   ```

2. **Chạy ngrok trong terminal khác:**
   ```bash
   ngrok http 5057
   ```

3. **Test thanh toán:**
   - Truy cập ứng dụng qua localhost:5057
   - Thực hiện đặt vé và chọn thanh toán VNPay
   - Hệ thống sẽ chuyển hướng đến VNPay sandbox
   - Sau khi thanh toán, VNPay sẽ gọi callback về ngrok URL

## Thông tin VNPay Sandbox

- **Terminal ID (TMN Code):** 1BB9SZY8
- **Secret Key:** SCZQJGDU1L7JSWQCNGF8Q0AMZBFPJ3VK
- **Payment URL:** https://sandbox.vnpayment.vn/paymentv2/vpcpay.html
- **Merchant Admin:** https://sandbox.vnpayment.vn/merchantv2/

## Thông tin test thanh toán

Sử dụng thông tin thẻ test của VNPay:
- **Số thẻ:** 9704198526191432198
- **Tên chủ thẻ:** NGUYEN VAN A
- **Ngày hết hạn:** 07/15
- **Mật khẩu OTP:** 123456

## Lưu ý quan trọng

1. **Ngrok URL thay đổi:** Mỗi lần restart ngrok, URL sẽ thay đổi. Cần cập nhật lại cấu hình.
2. **HTTPS required:** VNPay yêu cầu callback URL phải là HTTPS.
3. **Firewall:** Đảm bảo port 5057 không bị firewall chặn.
4. **Production:** Khi deploy production, thay thế ngrok URL bằng domain thật.

## Troubleshooting

1. **Callback không hoạt động:**
   - Kiểm tra ngrok có đang chạy không
   - Kiểm tra URL trong VNPay Merchant Admin
   - Kiểm tra logs trong ứng dụng

2. **Invalid signature:**
   - Kiểm tra Hash Secret có đúng không
   - Kiểm tra thứ tự parameters trong hash

3. **Payment URL không tạo được:**
   - Kiểm tra cấu hình VNPay trong appsettings
   - Kiểm tra VNPayHelper có được inject đúng không
