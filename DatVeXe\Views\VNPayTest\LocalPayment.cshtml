@model DatVeXe.Services.LocalTestPaymentViewModel
@{
    ViewData["Title"] = "VNPay Local Test Payment";
}

<div class="container py-4">
    <div class="row justify-content-center">
        <div class="col-lg-6">
            <div class="card shadow-lg border-0">
                <div class="card-header bg-primary text-white text-center py-4">
                    <h3 class="card-title mb-0">
                        <i class="bi bi-credit-card me-2"></i>VNPay Local Test
                    </h3>
                    <p class="mb-0 mt-2 opacity-75">Mô phỏng trang thanh toán VNPay</p>
                </div>
                <div class="card-body p-4">
                    <!-- Payment Information -->
                    <div class="alert alert-info">
                        <h6><i class="bi bi-info-circle me-2"></i>Thông tin thanh toán:</h6>
                        <ul class="mb-0">
                            <li><strong>Mã giao dịch:</strong> @Model.TxnRef</li>
                            <li><strong>Số tiền:</strong> @string.Format("{0:N0}", Model.Amount) VNĐ</li>
                            <li><strong>Nội dung:</strong> @Model.OrderInfo</li>
                        </ul>
                    </div>

                    <!-- Simulated VNPay Form -->
                    <form asp-action="ProcessLocalPayment" method="post" class="needs-validation" novalidate>
                        <input type="hidden" asp-for="TxnRef" />
                        <input type="hidden" asp-for="Amount" />
                        <input type="hidden" asp-for="OrderInfo" />
                        <input type="hidden" asp-for="ReturnUrl" />

                        <!-- Bank Selection -->
                        <div class="mb-4">
                            <label class="form-label fw-bold">Chọn ngân hàng:</label>
                            <div class="row g-2">
                                <div class="col-6">
                                    <div class="form-check">
                                        <input class="form-check-input" type="radio" name="BankCode" value="NCB" id="bankNCB" checked>
                                        <label class="form-check-label" for="bankNCB">
                                            <strong>NCB</strong><br>
                                            <small class="text-muted">Ngân hàng NCB</small>
                                        </label>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="form-check">
                                        <input class="form-check-input" type="radio" name="BankCode" value="VCB" id="bankVCB">
                                        <label class="form-check-label" for="bankVCB">
                                            <strong>VCB</strong><br>
                                            <small class="text-muted">Vietcombank</small>
                                        </label>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="form-check">
                                        <input class="form-check-input" type="radio" name="BankCode" value="TCB" id="bankTCB">
                                        <label class="form-check-label" for="bankTCB">
                                            <strong>TCB</strong><br>
                                            <small class="text-muted">Techcombank</small>
                                        </label>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="form-check">
                                        <input class="form-check-input" type="radio" name="BankCode" value="VTB" id="bankVTB">
                                        <label class="form-check-label" for="bankVTB">
                                            <strong>VTB</strong><br>
                                            <small class="text-muted">VietinBank</small>
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Test Result Selection -->
                        <div class="mb-4">
                            <label class="form-label fw-bold">Kết quả mô phỏng:</label>
                            <div class="row g-2">
                                <div class="col-6">
                                    <div class="form-check">
                                        <input class="form-check-input" type="radio" name="SimulateSuccess" value="true" id="resultSuccess" checked>
                                        <label class="form-check-label text-success" for="resultSuccess">
                                            <i class="bi bi-check-circle me-1"></i><strong>Thành công</strong><br>
                                            <small class="text-muted">Thanh toán thành công</small>
                                        </label>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="form-check">
                                        <input class="form-check-input" type="radio" name="SimulateSuccess" value="false" id="resultFailed">
                                        <label class="form-check-label text-danger" for="resultFailed">
                                            <i class="bi bi-x-circle me-1"></i><strong>Thất bại</strong><br>
                                            <small class="text-muted">Khách hàng hủy</small>
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Action Buttons -->
                        <div class="d-grid gap-2">
                            <button type="submit" class="btn btn-success btn-lg">
                                <i class="bi bi-credit-card me-2"></i>Xử lý thanh toán
                            </button>
                            <a href="javascript:history.back()" class="btn btn-outline-secondary">
                                <i class="bi bi-arrow-left me-2"></i>Quay lại
                            </a>
                        </div>
                    </form>

                    <!-- Test Info -->
                    <div class="mt-4 p-3 bg-light rounded">
                        <h6 class="text-muted mb-2">
                            <i class="bi bi-lightbulb me-1"></i>Thông tin test:
                        </h6>
                        <small class="text-muted">
                            Đây là trang mô phỏng VNPay để test local. Trong thực tế, khách hàng sẽ được chuyển đến trang VNPay thật để nhập thông tin thẻ.
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        // Add some visual feedback
        document.querySelectorAll('input[name="SimulateSuccess"]').forEach(radio => {
            radio.addEventListener('change', function() {
                const submitBtn = document.querySelector('button[type="submit"]');
                if (this.value === 'true') {
                    submitBtn.className = 'btn btn-success btn-lg';
                    submitBtn.innerHTML = '<i class="bi bi-check-circle me-2"></i>Xử lý thanh toán (Thành công)';
                } else {
                    submitBtn.className = 'btn btn-danger btn-lg';
                    submitBtn.innerHTML = '<i class="bi bi-x-circle me-2"></i>Xử lý thanh toán (Thất bại)';
                }
            });
        });

        // Simulate processing delay
        document.querySelector('form').addEventListener('submit', function(e) {
            const submitBtn = document.querySelector('button[type="submit"]');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="spinner-border spinner-border-sm me-2"></i>Đang xử lý...';
            
            // Add a small delay to simulate processing
            setTimeout(() => {
                this.submit();
            }, 1000);
            
            e.preventDefault();
        });
    </script>
}
