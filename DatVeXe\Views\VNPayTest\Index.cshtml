@{
    ViewData["Title"] = "VNPay Integration Test";
}

<div class="container py-4">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card shadow">
                <div class="card-header bg-primary text-white">
                    <h4 class="card-title mb-0">
                        <i class="bi bi-credit-card me-2"></i>VNPay Integration Test
                    </h4>
                </div>
                <div class="card-body">
                    @if (ViewBag.Error != null)
                    {
                        <div class="alert alert-danger">
                            <i class="bi bi-exclamation-triangle me-2"></i>@ViewBag.Error
                        </div>
                    }

                    @if (TempData["Error"] != null)
                    {
                        <div class="alert alert-danger">
                            <i class="bi bi-exclamation-triangle me-2"></i>@TempData["Error"]
                        </div>
                    }

                    <!-- Test Information -->
                    <div class="alert alert-info">
                        <h6><i class="bi bi-info-circle me-2"></i>Thông tin Test VNPay Sandbox:</h6>
                        <ul class="mb-0">
                            <li><strong>Số thẻ:</strong> 9704198526191432198</li>
                            <li><strong>Tên chủ thẻ:</strong> NGUYEN VAN A</li>
                            <li><strong>Ngày hết hạn:</strong> 07/15</li>
                            <li><strong>Mã OTP:</strong> 123456</li>
                        </ul>
                    </div>

                    <!-- Test Actions -->
                    <div class="row g-3">
                        <div class="col-md-6">
                            <div class="card border-success">
                                <div class="card-header bg-success text-white">
                                    <h6 class="mb-0">Test Payment Creation</h6>
                                </div>
                                <div class="card-body">
                                    <form asp-action="CreatePayment" method="post">
                                        <div class="mb-3">
                                            <label class="form-label">Số tiền test (VNĐ):</label>
                                            <input type="number" name="amount" class="form-control" value="100000" min="1000" max="10000000" step="1000">
                                        </div>
                                        <button type="submit" class="btn btn-success w-100">
                                            <i class="bi bi-play-circle me-2"></i>Tạo thanh toán test
                                        </button>
                                    </form>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="card border-info">
                                <div class="card-header bg-info text-white">
                                    <h6 class="mb-0">Test Callback Processing</h6>
                                </div>
                                <div class="card-body">
                                    <a href="@Url.Action("TestCallback")" class="btn btn-info w-100 mb-2">
                                        <i class="bi bi-arrow-repeat me-2"></i>Test Callback
                                    </a>
                                    <button type="button" class="btn btn-outline-info w-100" onclick="simulateCallback('00')">
                                        <i class="bi bi-check-circle me-2"></i>Simulate Success
                                    </button>
                                    <button type="button" class="btn btn-outline-danger w-100 mt-1" onclick="simulateCallback('24')">
                                        <i class="bi bi-x-circle me-2"></i>Simulate Cancel
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Additional Actions -->
                    <div class="row g-3 mt-3">
                        <div class="col-md-4">
                            <a href="@Url.Action("Instructions")" class="btn btn-outline-primary w-100">
                                <i class="bi bi-book me-2"></i>Hướng dẫn test
                            </a>
                        </div>
                        <div class="col-md-4">
                            <button type="button" class="btn btn-outline-secondary w-100" onclick="checkConfiguration()">
                                <i class="bi bi-gear me-2"></i>Kiểm tra cấu hình
                            </a>
                        </div>
                        <div class="col-md-4">
                            <a href="https://sandbox.vnpayment.vn/merchantv2/" target="_blank" class="btn btn-outline-warning w-100">
                                <i class="bi bi-box-arrow-up-right me-2"></i>Merchant Admin
                            </a>
                        </div>
                    </div>

                    <!-- Results Area -->
                    <div id="results" class="mt-4" style="display: none;">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0">Kết quả test:</h6>
                            </div>
                            <div class="card-body">
                                <pre id="resultContent" class="bg-light p-3 rounded"></pre>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        function simulateCallback(responseCode) {
            const amount = 100000;
            
            fetch('@Url.Action("SimulateCallback")', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                    'RequestVerificationToken': $('input[name="__RequestVerificationToken"]').val()
                },
                body: `responseCode=${responseCode}&amount=${amount}`
            })
            .then(response => response.json())
            .then(data => {
                showResults(data);
            })
            .catch(error => {
                console.error('Error:', error);
                alert('Có lỗi xảy ra: ' + error.message);
            });
        }

        function checkConfiguration() {
            fetch('@Url.Action("Configuration")')
            .then(response => response.json())
            .then(data => {
                showResults(data);
            })
            .catch(error => {
                console.error('Error:', error);
                alert('Có lỗi xảy ra: ' + error.message);
            });
        }

        function showResults(data) {
            document.getElementById('resultContent').textContent = JSON.stringify(data, null, 2);
            document.getElementById('results').style.display = 'block';
            document.getElementById('results').scrollIntoView({ behavior: 'smooth' });
        }
    </script>
}
