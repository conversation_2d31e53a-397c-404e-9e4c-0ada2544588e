using System.Text;
using System.Net.Http;
using System.Text.Json;
using DatVeXe.Models;

namespace DatVeXe.Services
{
    public class VNPayDemoService
    {
        private readonly ILogger<VNPayDemoService> _logger;
        private readonly IConfiguration _configuration;
        private readonly HttpClient _httpClient;

        public VNPayDemoService(ILogger<VNPayDemoService> logger, IConfiguration configuration, HttpClient httpClient)
        {
            _logger = logger;
            _configuration = configuration;
            _httpClient = httpClient;
        }

        /// <summary>
        /// Tạo URL thanh toán sử dụng VNPay Demo API
        /// </summary>
        public async Task<string> CreateDemoPaymentUrlAsync(VNPayRequest request)
        {
            try
            {
                var demoApiUrl = _configuration["VNPay:DemoPageUrl"] ?? "https://sandbox.vnpayment.vn/apis/vnpay-demo/";
                
                // Tạo demo payment request
                var demoRequest = new
                {
                    vnp_Version = _configuration["VNPay:Version"] ?? "2.1.0",
                    vnp_Command = _configuration["VNPay:Command"] ?? "pay",
                    vnp_TmnCode = _configuration["VNPay:TmnCode"],
                    vnp_Amount = ((long)(request.Amount * 100)).ToString(),
                    vnp_CurrCode = _configuration["VNPay:CurrCode"] ?? "VND",
                    vnp_TxnRef = request.TxnRef,
                    vnp_OrderInfo = request.OrderInfo,
                    vnp_OrderType = request.OrderType,
                    vnp_Locale = _configuration["VNPay:Locale"] ?? "vn",
                    vnp_ReturnUrl = request.ReturnUrl,
                    vnp_IpAddr = request.IpAddress,
                    vnp_CreateDate = request.CreateDate.ToString("yyyyMMddHHmmss"),
                    vnp_ExpireDate = request.ExpireDate?.ToString("yyyyMMddHHmmss")
                };

                _logger.LogInformation($"Creating VNPay demo payment for TxnRef: {request.TxnRef}");
                
                // Trả về URL demo với parameters
                var queryParams = new List<string>();
                
                foreach (var prop in demoRequest.GetType().GetProperties())
                {
                    var value = prop.GetValue(demoRequest)?.ToString();
                    if (!string.IsNullOrEmpty(value))
                    {
                        queryParams.Add($"{prop.Name}={Uri.EscapeDataString(value)}");
                    }
                }

                var demoUrl = $"{demoApiUrl}?{string.Join("&", queryParams)}";
                
                _logger.LogInformation($"VNPay demo URL created: {demoUrl.Substring(0, Math.Min(100, demoUrl.Length))}...");
                
                return demoUrl;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating VNPay demo payment URL");
                throw;
            }
        }

        /// <summary>
        /// Kiểm tra xem có sử dụng demo mode không
        /// </summary>
        public bool IsDemoMode()
        {
            return !string.IsNullOrEmpty(_configuration["VNPay:DemoPageUrl"]);
        }

        /// <summary>
        /// Lấy thông tin demo page
        /// </summary>
        public VNPayDemoInfo GetDemoInfo()
        {
            return new VNPayDemoInfo
            {
                DemoPageUrl = _configuration["VNPay:DemoPageUrl"] ?? "",
                TmnCode = _configuration["VNPay:TmnCode"] ?? "",
                IsEnabled = IsDemoMode(),
                TestCardInfo = new VNPayTestCardInfo
                {
                    CardNumber = "9704198526191432198",
                    CardHolder = "NGUYEN VAN A",
                    ExpiryDate = "07/15",
                    OTP = "123456"
                }
            };
        }

        /// <summary>
        /// Tạo URL test với VNPay Demo page
        /// </summary>
        public string CreateTestUrl(decimal amount = 100000, string orderInfo = "Test payment")
        {
            var demoPageUrl = _configuration["VNPay:DemoPageUrl"] ?? "https://sandbox.vnpayment.vn/apis/vnpay-demo/";
            var txnRef = DateTime.Now.ToString("yyyyMMddHHmmss");
            var returnUrl = "https://localhost:5057/Booking/PaymentCallback";

            var parameters = new Dictionary<string, string>
            {
                {"vnp_Version", "2.1.0"},
                {"vnp_Command", "pay"},
                {"vnp_TmnCode", _configuration["VNPay:TmnCode"] ?? "1BB9SZY8"},
                {"vnp_Amount", ((long)(amount * 100)).ToString()},
                {"vnp_CurrCode", "VND"},
                {"vnp_TxnRef", txnRef},
                {"vnp_OrderInfo", orderInfo},
                {"vnp_OrderType", "other"},
                {"vnp_Locale", "vn"},
                {"vnp_ReturnUrl", returnUrl},
                {"vnp_IpAddr", "127.0.0.1"},
                {"vnp_CreateDate", DateTime.Now.ToString("yyyyMMddHHmmss")}
            };

            var queryString = string.Join("&", parameters.Select(p => $"{p.Key}={Uri.EscapeDataString(p.Value)}"));
            return $"{demoPageUrl}?{queryString}";
        }
    }

    /// <summary>
    /// Thông tin VNPay Demo
    /// </summary>
    public class VNPayDemoInfo
    {
        public string DemoPageUrl { get; set; } = string.Empty;
        public string TmnCode { get; set; } = string.Empty;
        public bool IsEnabled { get; set; }
        public VNPayTestCardInfo TestCardInfo { get; set; } = new();
    }

    /// <summary>
    /// Thông tin thẻ test VNPay
    /// </summary>
    public class VNPayTestCardInfo
    {
        public string CardNumber { get; set; } = string.Empty;
        public string CardHolder { get; set; } = string.Empty;
        public string ExpiryDate { get; set; } = string.Empty;
        public string OTP { get; set; } = string.Empty;
    }
}
