@{
    ViewData["Title"] = "VNPay Integration Test Results";
    var results = ViewBag.TestResults as List<string>;
}

<div class="container py-4">
    <div class="row justify-content-center">
        <div class="col-lg-10">
            <div class="card shadow">
                <div class="card-header bg-info text-white">
                    <h4 class="card-title mb-0">
                        <i class="bi bi-check-circle me-2"></i>VNPay Integration Test Results
                    </h4>
                </div>
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <div>
                            <span class="badge bg-success me-2">
                                <i class="bi bi-play-fill me-1"></i>Tests Executed
                            </span>
                            <small class="text-muted">@DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")</small>
                        </div>
                        <div>
                            <button type="button" class="btn btn-outline-primary btn-sm" onclick="location.reload()">
                                <i class="bi bi-arrow-clockwise me-1"></i>Run Again
                            </button>
                            <a href="@Url.Action("Index")" class="btn btn-outline-secondary btn-sm">
                                <i class="bi bi-arrow-left me-1"></i>Back to Tests
                            </a>
                        </div>
                    </div>

                    <!-- Test Results -->
                    <div class="card border-0 bg-dark text-light">
                        <div class="card-header bg-secondary">
                            <h6 class="mb-0">
                                <i class="bi bi-terminal me-2"></i>Test Output
                            </h6>
                        </div>
                        <div class="card-body">
                            <pre class="mb-0" style="color: #00ff00; font-family: 'Courier New', monospace; font-size: 14px; line-height: 1.4;">@if (results != null)
{
    foreach (var line in results)
    {
        if (line.Contains("✅"))
        {
            <span style="color: #00ff00;">@line</span>
        }
        else if (line.Contains("❌"))
        {
            <span style="color: #ff4444;">@line</span>
        }
        else if (line.Contains("==="))
        {
            <span style="color: #ffff00; font-weight: bold;">@line</span>
        }
        else if (line.Contains("Testing") && line.Contains(":"))
        {
            <span style="color: #00ccff; font-weight: bold;">@line</span>
        }
        else
        {
            <span style="color: #cccccc;">@line</span>
        }
        @Html.Raw("\n")
    }
}
else
{
    <span style="color: #ff4444;">No test results available</span>
}</pre>
                        </div>
                    </div>

                    <!-- Summary -->
                    @if (results != null)
                    {
                        var passedTests = results.Count(r => r.Contains("✅"));
                        var failedTests = results.Count(r => r.Contains("❌"));
                        var totalTests = passedTests + failedTests;

                        <div class="row mt-4">
                            <div class="col-md-4">
                                <div class="card border-success">
                                    <div class="card-body text-center">
                                        <h3 class="text-success mb-1">@passedTests</h3>
                                        <p class="mb-0 text-muted">Passed</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="card border-danger">
                                    <div class="card-body text-center">
                                        <h3 class="text-danger mb-1">@failedTests</h3>
                                        <p class="mb-0 text-muted">Failed</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="card border-info">
                                    <div class="card-body text-center">
                                        <h3 class="text-info mb-1">@totalTests</h3>
                                        <p class="mb-0 text-muted">Total</p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        @if (failedTests == 0 && totalTests > 0)
                        {
                            <div class="alert alert-success mt-4">
                                <h5><i class="bi bi-check-circle me-2"></i>All Tests Passed!</h5>
                                <p class="mb-0">VNPay integration is working correctly. You can proceed with confidence.</p>
                            </div>
                        }
                        else if (failedTests > 0)
                        {
                            <div class="alert alert-warning mt-4">
                                <h5><i class="bi bi-exclamation-triangle me-2"></i>Some Tests Failed</h5>
                                <p class="mb-0">Please check the failed tests above and fix any issues before proceeding.</p>
                            </div>
                        }
                    }

                    <!-- Quick Actions -->
                    <div class="mt-4">
                        <h6>Quick Actions:</h6>
                        <div class="d-flex gap-2 flex-wrap">
                            <a href="@Url.Action("CreatePayment", new { amount = 50000 })" class="btn btn-success btn-sm">
                                <i class="bi bi-credit-card me-1"></i>Test 50K Payment
                            </a>
                            <a href="@Url.Action("CreatePayment", new { amount = 100000 })" class="btn btn-success btn-sm">
                                <i class="bi bi-credit-card me-1"></i>Test 100K Payment
                            </a>
                            <a href="@Url.Action("TestCallback")" class="btn btn-info btn-sm">
                                <i class="bi bi-arrow-repeat me-1"></i>Test Callback
                            </a>
                            <a href="@Url.Action("Configuration")" class="btn btn-secondary btn-sm" target="_blank">
                                <i class="bi bi-gear me-1"></i>View Config
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        // Auto-scroll to bottom of test output
        document.addEventListener('DOMContentLoaded', function() {
            const preElement = document.querySelector('pre');
            if (preElement) {
                preElement.scrollTop = preElement.scrollHeight;
            }
        });

        // Add copy to clipboard functionality
        function copyTestResults() {
            const testOutput = document.querySelector('pre').textContent;
            navigator.clipboard.writeText(testOutput).then(function() {
                alert('Test results copied to clipboard!');
            });
        }
    </script>
}
