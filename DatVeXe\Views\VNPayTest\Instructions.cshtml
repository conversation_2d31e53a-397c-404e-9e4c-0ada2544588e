@{
    ViewData["Title"] = "Hướng dẫn Test VNPay Local";
}

<div class="container py-4">
    <div class="row justify-content-center">
        <div class="col-lg-10">
            <div class="card shadow">
                <div class="card-header bg-success text-white">
                    <h4 class="card-title mb-0">
                        <i class="bi bi-book me-2"></i>Hướng dẫn Test VNPay Local (Không cần ngrok)
                    </h4>
                </div>
                <div class="card-body">
                    <!-- Overview -->
                    <div class="alert alert-success">
                        <h5><i class="bi bi-check-circle me-2"></i>Ưu điểm của Local Test Mode:</h5>
                        <ul class="mb-0">
                            <li>✅ <strong>Không cần ngrok</strong> - Test hoàn toàn trên localhost</li>
                            <li>✅ <strong>Không cần internet</strong> - <PERSON><PERSON> phỏng hoàn toàn offline</li>
                            <li>✅ <strong>Test nhanh</strong> - Không phải chờ redirect V<PERSON>ay thật</li>
                            <li>✅ <strong><PERSON><PERSON><PERSON> soát kết quả</strong> - Có thể test cả thành công và thất bại</li>
                            <li>✅ <strong>Debug dễ dàng</strong> - Xem được toàn bộ flow trong localhost</li>
                        </ul>
                    </div>

                    <!-- Step by step guide -->
                    <div class="row">
                        <div class="col-md-6">
                            <div class="card border-primary mb-4">
                                <div class="card-header bg-primary text-white">
                                    <h6 class="mb-0">Bước 1: Cấu hình</h6>
                                </div>
                                <div class="card-body">
                                    <p>Trong <code>appsettings.json</code>:</p>
                                    <pre class="bg-light p-2 rounded"><code>"VNPay": {
  "EnableLocalTest": true,
  "LocalTestMode": "simulation"
}</code></pre>
                                    <div class="alert alert-info mt-2">
                                        <small><i class="bi bi-info-circle me-1"></i>Đã được cấu hình sẵn!</small>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="card border-success mb-4">
                                <div class="card-header bg-success text-white">
                                    <h6 class="mb-0">Bước 2: Chạy ứng dụng</h6>
                                </div>
                                <div class="card-body">
                                    <p>Chạy lệnh:</p>
                                    <pre class="bg-light p-2 rounded"><code>dotnet run</code></pre>
                                    <p class="mt-2">Hoặc nhấn F5 trong Visual Studio</p>
                                    <div class="alert alert-success mt-2">
                                        <small><i class="bi bi-check-circle me-1"></i>Ứng dụng sẽ chạy trên https://localhost:5057</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Testing Flow -->
                    <div class="card border-warning mb-4">
                        <div class="card-header bg-warning text-dark">
                            <h6 class="mb-0">Bước 3: Test Payment Flow</h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="text-center mb-3">
                                        <div class="bg-primary text-white rounded-circle d-inline-flex align-items-center justify-content-center" style="width: 50px; height: 50px;">
                                            <strong>1</strong>
                                        </div>
                                        <h6 class="mt-2">Đặt vé</h6>
                                        <p class="small text-muted">Thực hiện flow đặt vé bình thường</p>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="text-center mb-3">
                                        <div class="bg-success text-white rounded-circle d-inline-flex align-items-center justify-content-center" style="width: 50px; height: 50px;">
                                            <strong>2</strong>
                                        </div>
                                        <h6 class="mt-2">Chọn VNPay</h6>
                                        <p class="small text-muted">Chọn phương thức thanh toán VNPay</p>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="text-center mb-3">
                                        <div class="bg-warning text-dark rounded-circle d-inline-flex align-items-center justify-content-center" style="width: 50px; height: 50px;">
                                            <strong>3</strong>
                                        </div>
                                        <h6 class="mt-2">Test Payment</h6>
                                        <p class="small text-muted">Sẽ chuyển đến trang test local</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Local Test Features -->
                    <div class="card border-info mb-4">
                        <div class="card-header bg-info text-white">
                            <h6 class="mb-0">Bước 4: Trang Test Local</h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <h6><i class="bi bi-bank me-1"></i>Chọn ngân hàng:</h6>
                                    <ul>
                                        <li>NCB (mặc định)</li>
                                        <li>VCB - Vietcombank</li>
                                        <li>TCB - Techcombank</li>
                                        <li>VTB - VietinBank</li>
                                    </ul>
                                </div>
                                <div class="col-md-6">
                                    <h6><i class="bi bi-toggle-on me-1"></i>Chọn kết quả:</h6>
                                    <ul>
                                        <li><span class="text-success">✅ Thành công</span> - Thanh toán thành công</li>
                                        <li><span class="text-danger">❌ Thất bại</span> - Khách hàng hủy</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Quick Test -->
                    <div class="card border-dark">
                        <div class="card-header bg-dark text-white">
                            <h6 class="mb-0">Test nhanh</h6>
                        </div>
                        <div class="card-body">
                            <p>Hoặc bạn có thể test trực tiếp từ đây:</p>
                            <div class="d-flex gap-2 flex-wrap">
                                <a href="@Url.Action("Index")" class="btn btn-primary">
                                    <i class="bi bi-play-circle me-1"></i>Trang Test VNPay
                                </a>
                                <a href="@Url.Action("Search", "Booking")" class="btn btn-success">
                                    <i class="bi bi-search me-1"></i>Tìm chuyến xe
                                </a>
                                <a href="@Url.Action("TestCallback")" class="btn btn-info">
                                    <i class="bi bi-arrow-repeat me-1"></i>Test Callback
                                </a>
                            </div>
                        </div>
                    </div>

                    <!-- Troubleshooting -->
                    <div class="mt-4">
                        <h5><i class="bi bi-tools me-2"></i>Troubleshooting</h5>
                        <div class="accordion">
                            <div class="accordion-item">
                                <h2 class="accordion-header">
                                    <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#troubleshoot1">
                                        Không chuyển đến trang test local?
                                    </button>
                                </h2>
                                <div id="troubleshoot1" class="accordion-collapse collapse">
                                    <div class="accordion-body">
                                        <p>Kiểm tra:</p>
                                        <ul>
                                            <li>Cấu hình <code>"EnableLocalTest": true</code> trong appsettings.json</li>
                                            <li>Service <code>VNPayLocalTestService</code> đã được đăng ký trong Program.cs</li>
                                            <li>Restart ứng dụng sau khi thay đổi cấu hình</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                            <div class="accordion-item">
                                <h2 class="accordion-header">
                                    <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#troubleshoot2">
                                        Callback không hoạt động?
                                    </button>
                                </h2>
                                <div id="troubleshoot2" class="accordion-collapse collapse">
                                    <div class="accordion-body">
                                        <p>Kiểm tra:</p>
                                        <ul>
                                            <li>URL callback trong PaymentRequest có đúng không</li>
                                            <li>BookingController.PaymentCallback có xử lý parameters không</li>
                                            <li>Xem log trong console để debug</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
