using System.Globalization;
using System.Net;
using System.Security.Cryptography;
using System.Text;

namespace DatVeXe.Services
{
    public class VNPayHelper
    {
        private readonly string _tmnCode;
        private readonly string _hashSecret;
        private readonly string _paymentUrl;
        private readonly string _version;
        private readonly string _command;
        private readonly string _currCode;
        private readonly string _locale;

        public VNPayHelper(IConfiguration configuration)
        {
            _tmnCode = configuration["VNPay:TmnCode"] ?? throw new ArgumentNullException("VNPay:TmnCode");
            _hashSecret = configuration["VNPay:HashSecret"] ?? throw new ArgumentNullException("VNPay:HashSecret");
            _paymentUrl = configuration["VNPay:PaymentUrl"] ?? throw new ArgumentNullException("VNPay:PaymentUrl");
            _version = configuration["VNPay:Version"] ?? "2.1.0";
            _command = configuration["VNPay:Command"] ?? "pay";
            _currCode = configuration["VNPay:CurrCode"] ?? "VND";
            _locale = configuration["VNPay:Locale"] ?? "vn";
        }

        public string CreatePaymentUrl(VNPayRequest request)
        {
            var vnpay = new SortedDictionary<string, string>();

            // Thông tin bắt buộc theo thứ tự alphabet
            vnpay.Add("vnp_Amount", ((long)(request.Amount * 100)).ToString());
            vnpay.Add("vnp_Command", _command);
            vnpay.Add("vnp_CreateDate", request.CreateDate.ToString("yyyyMMddHHmmss"));
            vnpay.Add("vnp_CurrCode", _currCode);
            vnpay.Add("vnp_IpAddr", request.IpAddress);
            vnpay.Add("vnp_Locale", _locale);
            vnpay.Add("vnp_OrderInfo", request.OrderInfo);
            vnpay.Add("vnp_OrderType", request.OrderType);
            vnpay.Add("vnp_ReturnUrl", request.ReturnUrl);
            vnpay.Add("vnp_TmnCode", _tmnCode);
            vnpay.Add("vnp_TxnRef", request.TxnRef);
            vnpay.Add("vnp_Version", _version);

            // Thông tin tùy chọn
            if (!string.IsNullOrEmpty(request.BankCode))
            {
                vnpay.Add("vnp_BankCode", request.BankCode);
            }

            if (request.ExpireDate.HasValue)
            {
                vnpay.Add("vnp_ExpireDate", request.ExpireDate.Value.ToString("yyyyMMddHHmmss"));
            }

            // Tạo chuỗi hash
            var hashData = new StringBuilder();
            foreach (var kvp in vnpay)
            {
                if (!string.IsNullOrEmpty(kvp.Value))
                {
                    hashData.Append(WebUtility.UrlEncode(kvp.Key) + "=" + WebUtility.UrlEncode(kvp.Value) + "&");
                }
            }

            var queryString = hashData.ToString();
            if (queryString.Length > 0)
            {
                queryString = queryString.Remove(queryString.Length - 1, 1);
            }

            var signData = queryString;
            var vnpSecureHash = HmacSHA512(_hashSecret, signData);
            queryString += "&vnp_SecureHash=" + vnpSecureHash;

            return _paymentUrl + "?" + queryString;
        }

        public VNPayResponse ProcessCallback(IQueryCollection queryParams)
        {
            try
            {
                var vnpay = new SortedDictionary<string, string>();

                foreach (var param in queryParams)
                {
                    if (!string.IsNullOrEmpty(param.Value) && param.Key.StartsWith("vnp_"))
                    {
                        vnpay.Add(param.Key, param.Value.ToString());
                    }
                }

                var vnp_SecureHash = queryParams["vnp_SecureHash"];
                vnpay.Remove("vnp_SecureHash");

                // Tạo chuỗi hash để kiểm tra
                var hashData = new StringBuilder();
                foreach (var kvp in vnpay)
                {
                    if (!string.IsNullOrEmpty(kvp.Value))
                    {
                        hashData.Append(WebUtility.UrlEncode(kvp.Key) + "=" + WebUtility.UrlEncode(kvp.Value) + "&");
                    }
                }

                var queryString = hashData.ToString();
                if (queryString.Length > 0)
                {
                    queryString = queryString.Remove(queryString.Length - 1, 1);
                }

                var checkSum = HmacSHA512(_hashSecret, queryString);
                var isValidSignature = checkSum.Equals(vnp_SecureHash, StringComparison.InvariantCultureIgnoreCase);

                // Parse amount safely
                decimal amount = 0;
                if (decimal.TryParse(queryParams["vnp_Amount"].ToString(), out decimal parsedAmount))
                {
                    amount = parsedAmount / 100;
                }

                return new VNPayResponse
                {
                    Success = isValidSignature && queryParams["vnp_ResponseCode"].ToString() == "00",
                    PaymentMethod = "VNPay",
                    OrderDescription = queryParams["vnp_OrderInfo"].ToString() ?? "",
                    OrderId = queryParams["vnp_TxnRef"].ToString() ?? "",
                    PaymentId = queryParams["vnp_TransactionNo"].ToString() ?? "",
                    TransactionId = queryParams["vnp_TxnRef"].ToString() ?? "",
                    Token = queryParams["vnp_SecureHash"].ToString() ?? "",
                    VnPayResponseCode = queryParams["vnp_ResponseCode"].ToString() ?? "",
                    Amount = amount,
                    BankCode = queryParams["vnp_BankCode"].ToString() ?? "",
                    BankTranNo = queryParams["vnp_BankTranNo"].ToString() ?? "",
                    CardType = queryParams["vnp_CardType"].ToString() ?? "",
                    PayDate = queryParams["vnp_PayDate"].ToString() ?? "",
                    IsValidSignature = isValidSignature
                };
            }
            catch (Exception ex)
            {
                // Log error and return failed response
                return new VNPayResponse
                {
                    Success = false,
                    PaymentMethod = "VNPay",
                    VnPayResponseCode = "99", // Error code
                    IsValidSignature = false,
                    OrderDescription = $"Error processing callback: {ex.Message}"
                };
            }
        }

        private string HmacSHA512(string key, string inputData)
        {
            var hash = new StringBuilder();
            var keyBytes = Encoding.UTF8.GetBytes(key);
            var inputBytes = Encoding.UTF8.GetBytes(inputData);
            using (var hmac = new HMACSHA512(keyBytes))
            {
                var hashValue = hmac.ComputeHash(inputBytes);
                foreach (var theByte in hashValue)
                {
                    hash.Append(theByte.ToString("x2"));
                }
            }

            return hash.ToString();
        }
    }

    public class VNPayRequest
    {
        public string TxnRef { get; set; } = string.Empty;
        public decimal Amount { get; set; }
        public string OrderInfo { get; set; } = string.Empty;
        public string OrderType { get; set; } = "other";
        public string ReturnUrl { get; set; } = string.Empty;
        public string IpAddress { get; set; } = string.Empty;
        public DateTime CreateDate { get; set; } = DateTime.Now;
        public DateTime? ExpireDate { get; set; }
        public string? BankCode { get; set; }
    }

    public class VNPayResponse
    {
        public bool Success { get; set; }
        public string PaymentMethod { get; set; } = string.Empty;
        public string OrderDescription { get; set; } = string.Empty;
        public string OrderId { get; set; } = string.Empty;
        public string PaymentId { get; set; } = string.Empty;
        public string TransactionId { get; set; } = string.Empty;
        public string Token { get; set; } = string.Empty;
        public string VnPayResponseCode { get; set; } = string.Empty;
        public decimal Amount { get; set; }
        public string BankCode { get; set; } = string.Empty;
        public string BankTranNo { get; set; } = string.Empty;
        public string CardType { get; set; } = string.Empty;
        public string PayDate { get; set; } = string.Empty;
        public bool IsValidSignature { get; set; }
    }
}
