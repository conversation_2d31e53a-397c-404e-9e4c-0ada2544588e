using Microsoft.AspNetCore.Mvc;
using DatVeXe.Services;
using DatVeXe.Helpers;
using DatVeXe.Models;

namespace DatVeXe.Controllers
{
    public class VNPayTestController : Controller
    {
        private readonly IPaymentService _paymentService;
        private readonly VNPayHelper _vnPayHelper;
        private readonly VNPayLocalTestService _localTestService;
        private readonly VNPayDemoService _demoService;
        private readonly ILogger<VNPayTestController> _logger;
        private readonly IConfiguration _configuration;

        public VNPayTestController(
            IPaymentService paymentService,
            VNPayHelper vnPayHelper,
            VNPayLocalTestService localTestService,
            VNPayDemoService demoService,
            ILogger<VNPayTestController> logger,
            IConfiguration configuration)
        {
            _paymentService = paymentService;
            _vnPayHelper = vnPayHelper;
            _localTestService = localTestService;
            _demoService = demoService;
            _logger = logger;
            _configuration = configuration;
        }

        // GET: VNPayTest
        public IActionResult Index()
        {
            // Validate configuration
            if (!VNPayTestHelper.ValidateConfiguration(_configuration))
            {
                ViewBag.Error = "VNPay configuration is invalid or missing";
                return View();
            }

            // Kiểm tra chế độ hiện tại
            ViewBag.IsLocalTest = _localTestService.IsLocalTestMode();
            ViewBag.IsDemoMode = _demoService.IsDemoMode();
            ViewBag.IsSandboxMode = !_localTestService.IsLocalTestMode() && !_demoService.IsDemoMode();
            ViewBag.DemoInfo = _demoService.GetDemoInfo();
            ViewBag.TestData = VNPayTestHelper.TestData.TestCardNumber;

            return View();
        }

        // POST: VNPayTest/CreatePayment
        [HttpPost]
        public async Task<IActionResult> CreatePayment(decimal amount = 100000)
        {
            try
            {
                _logger.LogInformation($"Creating test VNPay payment for amount: {amount}");

                var testRequest = VNPayTestHelper.CreateTestPaymentRequest(
                    ticketId: new Random().Next(10000, 99999),
                    amount: amount
                );

                var result = await _paymentService.CreatePaymentAsync(testRequest);

                if (result.Success && !string.IsNullOrEmpty(result.PaymentUrl))
                {
                    _logger.LogInformation($"Test payment URL created successfully: {result.PaymentUrl}");
                    return Redirect(result.PaymentUrl);
                }
                else
                {
                    _logger.LogError($"Failed to create test payment: {result.Message}");
                    TempData["Error"] = $"Không thể tạo thanh toán test: {result.Message}";
                    return RedirectToAction("Index");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating test payment");
                TempData["Error"] = $"Lỗi hệ thống: {ex.Message}";
                return RedirectToAction("Index");
            }
        }

        // GET: VNPayTest/TestCallback
        public async Task<IActionResult> TestCallback()
        {
            try
            {
                _logger.LogInformation("Testing VNPay callback processing");

                // Test success callback
                var successParams = VNPayTestHelper.CreateSuccessCallbackParams();
                var successResult = await _paymentService.ProcessPaymentCallbackAsync("12345", successParams);

                // Test failed callback
                var failedParams = VNPayTestHelper.CreateFailedCallbackParams();
                var failedResult = await _paymentService.ProcessPaymentCallbackAsync("12346", failedParams);

                ViewBag.SuccessResult = successResult;
                ViewBag.FailedResult = failedResult;

                return View();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error testing callback");
                TempData["Error"] = $"Lỗi test callback: {ex.Message}";
                return RedirectToAction("Index");
            }
        }

        // GET: VNPayTest/Instructions
        public IActionResult Instructions()
        {
            return View();
        }

        // GET: VNPayTest/Configuration
        public IActionResult Configuration()
        {
            var config = new
            {
                TmnCode = _configuration["VNPay:TmnCode"],
                PaymentUrl = _configuration["VNPay:PaymentUrl"],
                ReturnUrl = _configuration["VNPay:ReturnUrl"],
                Version = _configuration["VNPay:Version"],
                IsValid = VNPayTestHelper.ValidateConfiguration(_configuration)
            };

            return Json(config);
        }

        // POST: VNPayTest/SimulateCallback
        [HttpPost]
        public async Task<IActionResult> SimulateCallback(string responseCode = "00", decimal amount = 100000)
        {
            try
            {
                var txnRef = new Random().Next(10000, 99999).ToString();
                var parameters = new Dictionary<string, string>
                {
                    {"vnp_Amount", ((long)(amount * 100)).ToString()},
                    {"vnp_BankCode", "NCB"},
                    {"vnp_BankTranNo", "VNP" + DateTime.Now.ToString("yyyyMMddHHmmss")},
                    {"vnp_CardType", "ATM"},
                    {"vnp_OrderInfo", $"Test payment {txnRef}"},
                    {"vnp_PayDate", DateTime.Now.ToString("yyyyMMddHHmmss")},
                    {"vnp_ResponseCode", responseCode},
                    {"vnp_TmnCode", _configuration["VNPay:TmnCode"]},
                    {"vnp_TransactionNo", DateTime.Now.ToString("yyyyMMddHHmmss")},
                    {"vnp_TransactionStatus", responseCode == "00" ? "00" : "02"},
                    {"vnp_TxnRef", txnRef}
                };

                // Create proper signature
                var queryCollection = new Microsoft.AspNetCore.Http.QueryCollection(
                    parameters.ToDictionary(kvp => kvp.Key, kvp => new Microsoft.Extensions.Primitives.StringValues(kvp.Value))
                );

                var vnpayResponse = _vnPayHelper.ProcessCallback(queryCollection);
                var result = await _paymentService.ProcessPaymentCallbackAsync(txnRef, parameters);

                return Json(new
                {
                    success = true,
                    vnpayResponse = vnpayResponse,
                    paymentResult = result,
                    responseCodeMeaning = VNPayTestHelper.GetResponseCodeMeaning(responseCode)
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error simulating callback");
                return Json(new { success = false, error = ex.Message });
            }
        }

        // GET: VNPayTest/LocalPayment - Simulate VNPay payment page
        public IActionResult LocalPayment(string txnRef, decimal amount, string orderInfo, string returnUrl)
        {
            var model = new LocalTestPaymentViewModel
            {
                TxnRef = txnRef,
                Amount = amount,
                OrderInfo = orderInfo,
                ReturnUrl = returnUrl
            };

            return View(model);
        }

        // POST: VNPayTest/ProcessLocalPayment - Process local test payment
        [HttpPost]
        public async Task<IActionResult> ProcessLocalPayment(LocalTestPaymentViewModel model)
        {
            try
            {
                _logger.LogInformation($"Processing local test payment: TxnRef={model.TxnRef}, Success={model.SimulateSuccess}");

                // Simulate VNPay callback
                var callbackParams = _localTestService.SimulateVNPayCallback(
                    model.TxnRef,
                    model.Amount,
                    model.SimulateSuccess,
                    model.BankCode
                );

                // Build return URL with parameters
                var returnUrl = model.ReturnUrl;
                if (!string.IsNullOrEmpty(returnUrl))
                {
                    var separator = returnUrl.Contains('?') ? '&' : '?';
                    var queryString = string.Join("&", callbackParams.Select(p => $"{p.Key}={Uri.EscapeDataString(p.Value)}"));
                    returnUrl = $"{returnUrl}{separator}{queryString}";
                }

                _logger.LogInformation($"Redirecting to: {returnUrl}");
                return Redirect(returnUrl);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing local test payment");
                TempData["Error"] = $"Lỗi xử lý thanh toán test: {ex.Message}";
                return RedirectToAction("Index");
            }
        }

        // GET: VNPayTest/RunIntegrationTests
        public IActionResult RunIntegrationTests()
        {
            var results = new List<string>();

            try
            {
                results.Add("=== VNPay Integration Tests ===");
                results.Add("");

                // Test 1: VNPay URL Creation
                results.Add("1. Testing VNPay Payment URL Creation:");
                try
                {
                    var vnpayRequest = new VNPayRequest
                    {
                        TxnRef = "12345",
                        Amount = 100000,
                        OrderInfo = "Test payment",
                        ReturnUrl = "https://localhost:5057/Booking/PaymentCallback",
                        IpAddress = "127.0.0.1",
                        CreateDate = DateTime.Now,
                        ExpireDate = DateTime.Now.AddMinutes(15)
                    };

                    var paymentUrl = _vnPayHelper.CreatePaymentUrl(vnpayRequest);

                    if (!string.IsNullOrEmpty(paymentUrl) && paymentUrl.Contains("vnp_TmnCode"))
                    {
                        results.Add("✅ VNPay URL creation test PASSED");
                        results.Add($"URL: {paymentUrl.Substring(0, Math.Min(100, paymentUrl.Length))}...");
                    }
                    else
                    {
                        results.Add("❌ VNPay URL creation test FAILED");
                    }
                }
                catch (Exception ex)
                {
                    results.Add($"❌ VNPay URL creation test ERROR: {ex.Message}");
                }
                results.Add("");

                // Test 2: Local Test Service
                results.Add("2. Testing Local Test Service:");
                try
                {
                    results.Add($"Local test mode enabled: {_localTestService.IsLocalTestMode()}");
                    results.Add($"Test mode: {_localTestService.GetTestMode()}");

                    var callbackParams = _localTestService.SimulateVNPayCallback("12345", 100000, true);

                    if (callbackParams.ContainsKey("vnp_ResponseCode") && callbackParams["vnp_ResponseCode"] == "00")
                    {
                        results.Add("✅ Local test service test PASSED");
                        results.Add($"Generated {callbackParams.Count} callback parameters");
                    }
                    else
                    {
                        results.Add("❌ Local test service test FAILED");
                    }
                }
                catch (Exception ex)
                {
                    results.Add($"❌ Local test service test ERROR: {ex.Message}");
                }
                results.Add("");

                // Test 3: VNPay Callback Processing
                results.Add("3. Testing VNPay Callback Processing:");
                try
                {
                    var queryCollection = new Microsoft.AspNetCore.Http.QueryCollection(
                        new Dictionary<string, Microsoft.Extensions.Primitives.StringValues>
                        {
                            {"vnp_Amount", "********"},
                            {"vnp_BankCode", "NCB"},
                            {"vnp_BankTranNo", "VNP********"},
                            {"vnp_CardType", "ATM"},
                            {"vnp_OrderInfo", "Test payment"},
                            {"vnp_PayDate", "**************"},
                            {"vnp_ResponseCode", "00"},
                            {"vnp_TmnCode", "1BB9SZY8"},
                            {"vnp_TransactionNo", "********"},
                            {"vnp_TransactionStatus", "00"},
                            {"vnp_TxnRef", "12345"},
                            {"vnp_SecureHash", "test_hash"}
                        });

                    var response = _vnPayHelper.ProcessCallback(queryCollection);

                    if (response.VnPayResponseCode == "00")
                    {
                        results.Add("✅ VNPay callback processing test PASSED");
                        results.Add($"Amount: {response.Amount}, Transaction ID: {response.TransactionId}");
                    }
                    else
                    {
                        results.Add("❌ VNPay callback processing test FAILED");
                    }
                }
                catch (Exception ex)
                {
                    results.Add($"❌ VNPay callback processing test ERROR: {ex.Message}");
                }

                results.Add("");
                results.Add("=== Tests Completed ===");
            }
            catch (Exception ex)
            {
                results.Add($"FATAL ERROR: {ex.Message}");
            }

            ViewBag.TestResults = results;
            return View();
        }

        // GET: VNPayTest/TestDemo
        public IActionResult TestDemo(decimal amount = 100000)
        {
            try
            {
                if (!_demoService.IsDemoMode())
                {
                    TempData["Error"] = "Demo mode is not enabled";
                    return RedirectToAction("Index");
                }

                var demoUrl = _demoService.CreateTestUrl(amount, $"Test payment {DateTime.Now:HHmmss}");
                _logger.LogInformation($"Redirecting to VNPay Demo: {demoUrl}");

                return Redirect(demoUrl);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating demo test URL");
                TempData["Error"] = $"Lỗi tạo demo URL: {ex.Message}";
                return RedirectToAction("Index");
            }
        }

        // GET: VNPayTest/DemoInfo
        public IActionResult DemoInfo()
        {
            var demoInfo = _demoService.GetDemoInfo();
            return Json(demoInfo);
        }
    }
}
