using Microsoft.AspNetCore.Mvc;
using DatVeXe.Services;
using DatVeXe.Helpers;
using DatVeXe.Models;

namespace DatVeXe.Controllers
{
    public class VNPayTestController : Controller
    {
        private readonly IPaymentService _paymentService;
        private readonly VNPayHelper _vnPayHelper;
        private readonly VNPayLocalTestService _localTestService;
        private readonly ILogger<VNPayTestController> _logger;
        private readonly IConfiguration _configuration;

        public VNPayTestController(
            IPaymentService paymentService,
            VNPayHelper vnPayHelper,
            VNPayLocalTestService localTestService,
            ILogger<VNPayTestController> logger,
            IConfiguration configuration)
        {
            _paymentService = paymentService;
            _vnPayHelper = vnPayHelper;
            _localTestService = localTestService;
            _logger = logger;
            _configuration = configuration;
        }

        // GET: VNPayTest
        public IActionResult Index()
        {
            // Validate configuration
            if (!VNPayTestHelper.ValidateConfiguration(_configuration))
            {
                ViewBag.Error = "VNPay configuration is invalid or missing";
                return View();
            }

            ViewBag.TestData = VNPayTestHelper.TestData.TestCardNumber;
            return View();
        }

        // POST: VNPayTest/CreatePayment
        [HttpPost]
        public async Task<IActionResult> CreatePayment(decimal amount = 100000)
        {
            try
            {
                _logger.LogInformation($"Creating test VNPay payment for amount: {amount}");

                var testRequest = VNPayTestHelper.CreateTestPaymentRequest(
                    ticketId: new Random().Next(10000, 99999),
                    amount: amount
                );

                var result = await _paymentService.CreatePaymentAsync(testRequest);

                if (result.Success && !string.IsNullOrEmpty(result.PaymentUrl))
                {
                    _logger.LogInformation($"Test payment URL created successfully: {result.PaymentUrl}");
                    return Redirect(result.PaymentUrl);
                }
                else
                {
                    _logger.LogError($"Failed to create test payment: {result.Message}");
                    TempData["Error"] = $"Không thể tạo thanh toán test: {result.Message}";
                    return RedirectToAction("Index");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating test payment");
                TempData["Error"] = $"Lỗi hệ thống: {ex.Message}";
                return RedirectToAction("Index");
            }
        }

        // GET: VNPayTest/TestCallback
        public async Task<IActionResult> TestCallback()
        {
            try
            {
                _logger.LogInformation("Testing VNPay callback processing");

                // Test success callback
                var successParams = VNPayTestHelper.CreateSuccessCallbackParams();
                var successResult = await _paymentService.ProcessPaymentCallbackAsync("12345", successParams);

                // Test failed callback
                var failedParams = VNPayTestHelper.CreateFailedCallbackParams();
                var failedResult = await _paymentService.ProcessPaymentCallbackAsync("12346", failedParams);

                ViewBag.SuccessResult = successResult;
                ViewBag.FailedResult = failedResult;

                return View();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error testing callback");
                TempData["Error"] = $"Lỗi test callback: {ex.Message}";
                return RedirectToAction("Index");
            }
        }

        // GET: VNPayTest/Instructions
        public IActionResult Instructions()
        {
            return View();
        }

        // GET: VNPayTest/Configuration
        public IActionResult Configuration()
        {
            var config = new
            {
                TmnCode = _configuration["VNPay:TmnCode"],
                PaymentUrl = _configuration["VNPay:PaymentUrl"],
                ReturnUrl = _configuration["VNPay:ReturnUrl"],
                Version = _configuration["VNPay:Version"],
                IsValid = VNPayTestHelper.ValidateConfiguration(_configuration)
            };

            return Json(config);
        }

        // POST: VNPayTest/SimulateCallback
        [HttpPost]
        public async Task<IActionResult> SimulateCallback(string responseCode = "00", decimal amount = 100000)
        {
            try
            {
                var txnRef = new Random().Next(10000, 99999).ToString();
                var parameters = new Dictionary<string, string>
                {
                    {"vnp_Amount", ((long)(amount * 100)).ToString()},
                    {"vnp_BankCode", "NCB"},
                    {"vnp_BankTranNo", "VNP" + DateTime.Now.ToString("yyyyMMddHHmmss")},
                    {"vnp_CardType", "ATM"},
                    {"vnp_OrderInfo", $"Test payment {txnRef}"},
                    {"vnp_PayDate", DateTime.Now.ToString("yyyyMMddHHmmss")},
                    {"vnp_ResponseCode", responseCode},
                    {"vnp_TmnCode", _configuration["VNPay:TmnCode"]},
                    {"vnp_TransactionNo", DateTime.Now.ToString("yyyyMMddHHmmss")},
                    {"vnp_TransactionStatus", responseCode == "00" ? "00" : "02"},
                    {"vnp_TxnRef", txnRef}
                };

                // Create proper signature
                var queryCollection = new Microsoft.AspNetCore.Http.QueryCollection(
                    parameters.ToDictionary(kvp => kvp.Key, kvp => new Microsoft.Extensions.Primitives.StringValues(kvp.Value))
                );

                var vnpayResponse = _vnPayHelper.ProcessCallback(queryCollection);
                var result = await _paymentService.ProcessPaymentCallbackAsync(txnRef, parameters);

                return Json(new
                {
                    success = true,
                    vnpayResponse = vnpayResponse,
                    paymentResult = result,
                    responseCodeMeaning = VNPayTestHelper.GetResponseCodeMeaning(responseCode)
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error simulating callback");
                return Json(new { success = false, error = ex.Message });
            }
        }

        // GET: VNPayTest/LocalPayment - Simulate VNPay payment page
        public IActionResult LocalPayment(string txnRef, decimal amount, string orderInfo, string returnUrl)
        {
            var model = new LocalTestPaymentViewModel
            {
                TxnRef = txnRef,
                Amount = amount,
                OrderInfo = orderInfo,
                ReturnUrl = returnUrl
            };

            return View(model);
        }

        // POST: VNPayTest/ProcessLocalPayment - Process local test payment
        [HttpPost]
        public async Task<IActionResult> ProcessLocalPayment(LocalTestPaymentViewModel model)
        {
            try
            {
                _logger.LogInformation($"Processing local test payment: TxnRef={model.TxnRef}, Success={model.SimulateSuccess}");

                // Simulate VNPay callback
                var callbackParams = _localTestService.SimulateVNPayCallback(
                    model.TxnRef,
                    model.Amount,
                    model.SimulateSuccess,
                    model.BankCode
                );

                // Build return URL with parameters
                var returnUrl = model.ReturnUrl;
                if (!string.IsNullOrEmpty(returnUrl))
                {
                    var separator = returnUrl.Contains('?') ? '&' : '?';
                    var queryString = string.Join("&", callbackParams.Select(p => $"{p.Key}={Uri.EscapeDataString(p.Value)}"));
                    returnUrl = $"{returnUrl}{separator}{queryString}";
                }

                _logger.LogInformation($"Redirecting to: {returnUrl}");
                return Redirect(returnUrl);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing local test payment");
                TempData["Error"] = $"Lỗi xử lý thanh toán test: {ex.Message}";
                return RedirectToAction("Index");
            }
        }
    }
}
