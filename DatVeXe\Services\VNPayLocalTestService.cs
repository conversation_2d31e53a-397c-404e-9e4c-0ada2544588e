using Microsoft.AspNetCore.Mvc;
using DatVeXe.Models;

namespace DatVeXe.Services
{
    public class VNPayLocalTestService
    {
        private readonly ILogger<VNPayLocalTestService> _logger;
        private readonly IConfiguration _configuration;

        public VNPayLocalTestService(ILogger<VNPayLocalTestService> logger, IConfiguration configuration)
        {
            _logger = logger;
            _configuration = configuration;
        }

        /// <summary>
        /// Tạo URL test local thay vì redirect đến VNPay thật
        /// </summary>
        public string CreateLocalTestUrl(VNPayRequest request, string baseUrl)
        {
            var testParams = new Dictionary<string, string>
            {
                {"txnRef", request.TxnRef},
                {"amount", request.Amount.ToString()},
                {"orderInfo", request.OrderInfo},
                {"returnUrl", request.ReturnUrl}
            };

            var queryString = string.Join("&", testParams.Select(p => $"{p.Key}={Uri.EscapeDataString(p.Value)}"));
            return $"{baseUrl}/VNPayTest/LocalPayment?{queryString}";
        }

        /// <summary>
        /// Simulate VNPay callback response
        /// </summary>
        public Dictionary<string, string> SimulateVNPayCallback(string txnRef, decimal amount, bool isSuccess = true, string bankCode = "NCB")
        {
            var responseCode = isSuccess ? "00" : "24"; // 00 = success, 24 = cancelled
            var transactionNo = DateTime.Now.ToString("yyyyMMddHHmmss");
            var payDate = DateTime.Now.ToString("yyyyMMddHHmmss");

            var parameters = new Dictionary<string, string>
            {
                {"vnp_Amount", ((long)(amount * 100)).ToString()},
                {"vnp_BankCode", bankCode},
                {"vnp_BankTranNo", $"VNP{transactionNo}"},
                {"vnp_CardType", "ATM"},
                {"vnp_OrderInfo", $"Thanh toan ve {txnRef}"},
                {"vnp_PayDate", payDate},
                {"vnp_ResponseCode", responseCode},
                {"vnp_TmnCode", _configuration["VNPay:TmnCode"] ?? "1BB9SZY8"},
                {"vnp_TransactionNo", transactionNo},
                {"vnp_TransactionStatus", responseCode},
                {"vnp_TxnRef", txnRef}
            };

            // Tạo secure hash giả lập
            var hashSecret = _configuration["VNPay:HashSecret"] ?? "SCZQJGDU1L7JSWQCNGF8Q0AMZBFPJ3VK";
            var secureHash = GenerateTestSecureHash(parameters, hashSecret);
            parameters.Add("vnp_SecureHash", secureHash);

            _logger.LogInformation($"Simulated VNPay callback: TxnRef={txnRef}, Amount={amount}, Success={isSuccess}");

            return parameters;
        }

        /// <summary>
        /// Generate test secure hash (simplified for local testing)
        /// </summary>
        private string GenerateTestSecureHash(Dictionary<string, string> parameters, string hashSecret)
        {
            // Tạo hash đơn giản cho test local
            var dataToHash = string.Join("&", parameters.OrderBy(p => p.Key).Select(p => $"{p.Key}={p.Value}"));
            return $"TEST_HASH_{dataToHash.GetHashCode():X8}";
        }

        /// <summary>
        /// Check if local test mode is enabled
        /// </summary>
        public bool IsLocalTestMode()
        {
            return _configuration.GetValue<bool>("VNPay:EnableLocalTest", false);
        }

        /// <summary>
        /// Get test mode type
        /// </summary>
        public string GetTestMode()
        {
            return _configuration.GetValue<string>("VNPay:LocalTestMode", "simulation");
        }
    }

    /// <summary>
    /// Local test payment view model
    /// </summary>
    public class LocalTestPaymentViewModel
    {
        public string TxnRef { get; set; } = string.Empty;
        public decimal Amount { get; set; }
        public string OrderInfo { get; set; } = string.Empty;
        public string ReturnUrl { get; set; } = string.Empty;
        public string BankCode { get; set; } = "NCB";
        public bool SimulateSuccess { get; set; } = true;
    }
}
