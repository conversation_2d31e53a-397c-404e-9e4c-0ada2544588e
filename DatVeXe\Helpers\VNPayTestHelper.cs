using Microsoft.Extensions.Configuration;
using DatVeXe.Services;

namespace DatVeXe.Helpers
{
    public static class VNPayTestHelper
    {
        // Thông tin test từ VNPay Sandbox
        public static class TestData
        {
            public const string TestCardNumber = "9704198526191432198";
            public const string TestCardHolder = "NGUYEN VAN A";
            public const string TestExpiryDate = "07/15";
            public const string TestOTP = "123456";
            
            // URLs từ VNPay
            public const string MerchantAdminUrl = "https://sandbox.vnpayment.vn/merchantv2/";
            public const string TestLoginUrl = "https://sandbox.vnpayment.vn/vnpaygw-sit-testing/user/login";
            
            // Thông tin đăng nhập test
            public const string TestUsername = "<EMAIL>";
            
            // Merchant Info
            public const string TmnCode = "1BB9SZY8";
            public const string HashSecret = "SCZQJGDU1L7JSWQCNGF8Q0AMZBFPJ3VK";
        }

        /// <summary>
        /// Tạo test payment request với thông tin mẫu
        /// </summary>
        public static PaymentRequest CreateTestPaymentRequest(int ticketId = 12345, decimal amount = 100000)
        {
            return new PaymentRequest
            {
                TicketId = ticketId,
                Amount = amount,
                Method = Models.PhuongThucThanhToan.VNPay,
                OrderInfo = $"Test payment for ticket {ticketId}",
                ReturnUrl = "https://localhost:5057/Booking/PaymentCallback",
                CancelUrl = "https://localhost:5057/Booking/PaymentCancel",
                CustomerName = "Nguyen Van Test",
                CustomerPhone = "**********",
                CustomerEmail = "<EMAIL>"
            };
        }

        /// <summary>
        /// Tạo test VNPay callback parameters (success case)
        /// </summary>
        public static Dictionary<string, string> CreateSuccessCallbackParams(string txnRef = "12345", decimal amount = 100000)
        {
            return new Dictionary<string, string>
            {
                {"vnp_Amount", ((long)(amount * 100)).ToString()},
                {"vnp_BankCode", "NCB"},
                {"vnp_BankTranNo", "VNP********"},
                {"vnp_CardType", "ATM"},
                {"vnp_OrderInfo", $"Test payment for ticket {txnRef}"},
                {"vnp_PayDate", DateTime.Now.ToString("yyyyMMddHHmmss")},
                {"vnp_ResponseCode", "00"}, // Success
                {"vnp_TmnCode", TestData.TmnCode},
                {"vnp_TransactionNo", "********"},
                {"vnp_TransactionStatus", "00"},
                {"vnp_TxnRef", txnRef},
                {"vnp_SecureHash", "dummy_hash"} // Sẽ được tính toán thực tế
            };
        }

        /// <summary>
        /// Tạo test VNPay callback parameters (failed case)
        /// </summary>
        public static Dictionary<string, string> CreateFailedCallbackParams(string txnRef = "12345", decimal amount = 100000)
        {
            return new Dictionary<string, string>
            {
                {"vnp_Amount", ((long)(amount * 100)).ToString()},
                {"vnp_BankCode", "NCB"},
                {"vnp_OrderInfo", $"Test payment for ticket {txnRef}"},
                {"vnp_PayDate", DateTime.Now.ToString("yyyyMMddHHmmss")},
                {"vnp_ResponseCode", "24"}, // Failed - Customer cancelled
                {"vnp_TmnCode", TestData.TmnCode},
                {"vnp_TransactionStatus", "02"},
                {"vnp_TxnRef", txnRef},
                {"vnp_SecureHash", "dummy_hash"}
            };
        }

        /// <summary>
        /// Validate VNPay configuration
        /// </summary>
        public static bool ValidateConfiguration(IConfiguration configuration)
        {
            var tmnCode = configuration["VNPay:TmnCode"];
            var hashSecret = configuration["VNPay:HashSecret"];
            var paymentUrl = configuration["VNPay:PaymentUrl"];

            if (string.IsNullOrEmpty(tmnCode) || string.IsNullOrEmpty(hashSecret) || string.IsNullOrEmpty(paymentUrl))
            {
                return false;
            }

            // Check if using correct sandbox values
            if (tmnCode != TestData.TmnCode || hashSecret != TestData.HashSecret)
            {
                Console.WriteLine("Warning: VNPay configuration doesn't match expected sandbox values");
            }

            return true;
        }

        /// <summary>
        /// Print test instructions
        /// </summary>
        public static void PrintTestInstructions()
        {
            Console.WriteLine("=== VNPay Sandbox Test Instructions ===");
            Console.WriteLine();
            Console.WriteLine("1. Merchant Admin Panel:");
            Console.WriteLine($"   URL: {TestData.MerchantAdminUrl}");
            Console.WriteLine($"   Username: {TestData.TestUsername}");
            Console.WriteLine("   Password: (Lấy mật khẩu từ email đăng ký)");
            Console.WriteLine();
            Console.WriteLine("2. Test Payment:");
            Console.WriteLine($"   Card Number: {TestData.TestCardNumber}");
            Console.WriteLine($"   Card Holder: {TestData.TestCardHolder}");
            Console.WriteLine($"   Expiry Date: {TestData.TestExpiryDate}");
            Console.WriteLine($"   OTP: {TestData.TestOTP}");
            Console.WriteLine();
            Console.WriteLine("3. Test Login:");
            Console.WriteLine($"   URL: {TestData.TestLoginUrl}");
            Console.WriteLine($"   Username: {TestData.TestUsername}");
            Console.WriteLine();
            Console.WriteLine("4. Test Flow:");
            Console.WriteLine("   - Chọn VNPay payment method");
            Console.WriteLine("   - Nhập thông tin thẻ test");
            Console.WriteLine("   - Nhập OTP khi được yêu cầu");
            Console.WriteLine("   - Kiểm tra callback và kết quả");
            Console.WriteLine();
            Console.WriteLine("==========================================");
        }

        /// <summary>
        /// Get VNPay response code meanings
        /// </summary>
        public static string GetResponseCodeMeaning(string responseCode)
        {
            return responseCode switch
            {
                "00" => "Giao dịch thành công",
                "07" => "Trừ tiền thành công. Giao dịch bị nghi ngờ (liên quan tới lừa đảo, giao dịch bất thường).",
                "09" => "Giao dịch không thành công do: Thẻ/Tài khoản của khách hàng chưa đăng ký dịch vụ InternetBanking tại ngân hàng.",
                "10" => "Giao dịch không thành công do: Khách hàng xác thực thông tin thẻ/tài khoản không đúng quá 3 lần",
                "11" => "Giao dịch không thành công do: Đã hết hạn chờ thanh toán. Xin quý khách vui lòng thực hiện lại giao dịch.",
                "12" => "Giao dịch không thành công do: Thẻ/Tài khoản của khách hàng bị khóa.",
                "13" => "Giao dịch không thành công do Quý khách nhập sai mật khẩu xác thực giao dịch (OTP).",
                "24" => "Giao dịch không thành công do: Khách hàng hủy giao dịch",
                "51" => "Giao dịch không thành công do: Tài khoản của quý khách không đủ số dư để thực hiện giao dịch.",
                "65" => "Giao dịch không thành công do: Tài khoản của Quý khách đã vượt quá hạn mức giao dịch trong ngày.",
                "75" => "Ngân hàng thanh toán đang bảo trì.",
                "79" => "Giao dịch không thành công do: KH nhập sai mật khẩu thanh toán quá số lần quy định.",
                "99" => "Các lỗi khác (lỗi còn lại, không có trong danh sách mã lỗi đã liệt kê)",
                _ => $"Mã lỗi không xác định: {responseCode}"
            };
        }
    }
}
