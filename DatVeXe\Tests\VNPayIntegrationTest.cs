using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.AspNetCore.Http;
using DatVeXe.Services;
using DatVeXe.Models;

namespace DatVeXe.Tests
{
    public class VNPayIntegrationTest
    {
        private readonly VNPayHelper _vnPayHelper;
        private readonly VNPayLocalTestService _localTestService;
        private readonly IConfiguration _configuration;

        public VNPayIntegrationTest()
        {
            // Setup configuration
            var configBuilder = new ConfigurationBuilder();
            configBuilder.AddInMemoryCollection(new Dictionary<string, string>
            {
                {"VNPay:TmnCode", "1BB9SZY8"},
                {"VNPay:HashSecret", "SCZQJGDU1L7JSWQCNGF8Q0AMZBFPJ3VK"},
                {"VNPay:PaymentUrl", "https://sandbox.vnpayment.vn/paymentv2/vpcpay.html"},
                {"VNPay:Version", "2.1.0"},
                {"VNPay:Command", "pay"},
                {"VNPay:CurrCode", "VND"},
                {"VNPay:Locale", "vn"},
                {"VNPay:EnableLocalTest", "true"},
                {"VNPay:LocalTestMode", "simulation"}
            });
            _configuration = configBuilder.Build();

            _vnPayHelper = new VNPayHelper(_configuration);
            _localTestService = new VNPayLocalTestService(new ConsoleLogger(), _configuration);
        }

        // Simple console logger for testing
        private class ConsoleLogger : ILogger<VNPayLocalTestService>
        {
            public IDisposable BeginScope<TState>(TState state) => null!;
            public bool IsEnabled(LogLevel logLevel) => true;
            public void Log<TState>(LogLevel logLevel, EventId eventId, TState state, Exception? exception, Func<TState, Exception?, string> formatter)
            {
                Console.WriteLine($"[{logLevel}] {formatter(state, exception)}");
            }
        }

        public void TestCreateVNPayPaymentUrl()
        {
            // Arrange
            var vnpayRequest = new VNPayRequest
            {
                TxnRef = "12345",
                Amount = 100000,
                OrderInfo = "Test payment",
                ReturnUrl = "https://localhost:5057/Booking/PaymentCallback",
                IpAddress = "127.0.0.1",
                CreateDate = DateTime.Now,
                ExpireDate = DateTime.Now.AddMinutes(15)
            };

            // Act
            var paymentUrl = _vnPayHelper.CreatePaymentUrl(vnpayRequest);

            // Assert
            Console.WriteLine($"Payment URL created: {!string.IsNullOrEmpty(paymentUrl)}");
            Console.WriteLine($"Payment URL: {paymentUrl}");

            if (!string.IsNullOrEmpty(paymentUrl) && paymentUrl.Contains("vnp_TmnCode"))
            {
                Console.WriteLine("✅ VNPay URL creation test PASSED");

                // Verify URL contains required parameters
                var uri = new Uri(paymentUrl);
                var query = System.Web.HttpUtility.ParseQueryString(uri.Query);

                Console.WriteLine($"vnp_TmnCode: {query["vnp_TmnCode"]}");
                Console.WriteLine($"vnp_Amount: {query["vnp_Amount"]}");
                Console.WriteLine($"vnp_TxnRef: {query["vnp_TxnRef"]}");
                Console.WriteLine($"vnp_SecureHash: {query["vnp_SecureHash"]}");
            }
            else
            {
                Console.WriteLine("❌ VNPay URL creation test FAILED");
            }
        }

        public void TestVNPayCallbackProcessing()
        {
            // Arrange - Simulate VNPay callback parameters
            var queryCollection = new QueryCollection(new Dictionary<string, Microsoft.Extensions.Primitives.StringValues>
            {
                {"vnp_Amount", "********"}, // 100,000 VND * 100
                {"vnp_BankCode", "NCB"},
                {"vnp_BankTranNo", "VNP********"},
                {"vnp_CardType", "ATM"},
                {"vnp_OrderInfo", "Test payment"},
                {"vnp_PayDate", "**************"},
                {"vnp_ResponseCode", "00"}, // Success code
                {"vnp_TmnCode", "1BB9SZY8"},
                {"vnp_TransactionNo", "********"},
                {"vnp_TransactionStatus", "00"},
                {"vnp_TxnRef", "12345"},
                {"vnp_SecureHash", "test_hash"} // This would be calculated properly in real scenario
            });

            // Act
            var response = _vnPayHelper.ProcessCallback(queryCollection);

            // Assert
            Console.WriteLine($"Callback processing result: {response.Success}");
            Console.WriteLine($"Response Code: {response.VnPayResponseCode}");
            Console.WriteLine($"Amount: {response.Amount}");
            Console.WriteLine($"Transaction ID: {response.TransactionId}");
            Console.WriteLine($"Is Valid Signature: {response.IsValidSignature}");

            if (response.VnPayResponseCode == "00")
            {
                Console.WriteLine("✅ VNPay callback processing test PASSED");
            }
            else
            {
                Console.WriteLine("❌ VNPay callback processing test FAILED");
            }
        }

        public void TestLocalTestService()
        {
            // Test local test mode
            Console.WriteLine($"Local test mode enabled: {_localTestService.IsLocalTestMode()}");
            Console.WriteLine($"Test mode: {_localTestService.GetTestMode()}");

            // Test simulate callback
            var callbackParams = _localTestService.SimulateVNPayCallback("12345", 100000, true);

            Console.WriteLine("Simulated callback parameters:");
            foreach (var param in callbackParams)
            {
                Console.WriteLine($"  {param.Key}: {param.Value}");
            }

            if (callbackParams.ContainsKey("vnp_ResponseCode") && callbackParams["vnp_ResponseCode"] == "00")
            {
                Console.WriteLine("✅ Local test service test PASSED");
            }
            else
            {
                Console.WriteLine("❌ Local test service test FAILED");
            }
        }

        public static void RunTests()
        {
            var test = new VNPayIntegrationTest();

            Console.WriteLine("=== VNPay Integration Tests ===");
            Console.WriteLine();

            Console.WriteLine("1. Testing VNPay Payment URL Creation:");
            test.TestCreateVNPayPaymentUrl();
            Console.WriteLine();

            Console.WriteLine("2. Testing VNPay Callback Processing:");
            test.TestVNPayCallbackProcessing();
            Console.WriteLine();

            Console.WriteLine("3. Testing Local Test Service:");
            test.TestLocalTestService();
            Console.WriteLine();

            Console.WriteLine("=== Tests Completed ===");
        }
    }
}
