using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.AspNetCore.Http;
using DatVeXe.Services;
using DatVeXe.Models;
using Moq;
using System.Collections.Specialized;

namespace DatVeXe.Tests
{
    public class VNPayIntegrationTest
    {
        private readonly VNPayHelper _vnPayHelper;
        private readonly PaymentService _paymentService;
        private readonly Mock<ILogger<PaymentService>> _mockLogger;
        private readonly Mock<IHttpContextAccessor> _mockHttpContextAccessor;
        private readonly IConfiguration _configuration;

        public VNPayIntegrationTest()
        {
            // Setup configuration
            var configBuilder = new ConfigurationBuilder();
            configBuilder.AddInMemoryCollection(new Dictionary<string, string>
            {
                {"VNPay:TmnCode", "1BB9SZY8"},
                {"VNPay:HashSecret", "SCZQJGDU1L7JSWQCNGF8Q0AMZBFPJ3VK"},
                {"VNPay:PaymentUrl", "https://sandbox.vnpayment.vn/paymentv2/vpcpay.html"},
                {"VNPay:Version", "2.1.0"},
                {"VNPay:Command", "pay"},
                {"VNPay:CurrCode", "VND"},
                {"VNPay:Locale", "vn"}
            });
            _configuration = configBuilder.Build();

            _vnPayHelper = new VNPayHelper(_configuration);
            _mockLogger = new Mock<ILogger<PaymentService>>();
            _mockHttpContextAccessor = new Mock<IHttpContextAccessor>();
            
            // Mock HttpContext
            var mockHttpContext = new Mock<HttpContext>();
            var mockConnection = new Mock<ConnectionInfo>();
            mockConnection.Setup(c => c.RemoteIpAddress).Returns(System.Net.IPAddress.Parse("127.0.0.1"));
            mockHttpContext.Setup(c => c.Connection).Returns(mockConnection.Object);
            _mockHttpContextAccessor.Setup(a => a.HttpContext).Returns(mockHttpContext.Object);

            _paymentService = new PaymentService(_mockLogger.Object, _configuration, _vnPayHelper, _mockHttpContextAccessor.Object);
        }

        public void TestCreateVNPayPaymentUrl()
        {
            // Arrange
            var request = new PaymentRequest
            {
                TicketId = 12345,
                Amount = 100000,
                Method = PhuongThucThanhToan.VNPay,
                OrderInfo = "Test payment",
                ReturnUrl = "https://localhost:5057/Booking/PaymentCallback"
            };

            // Act
            var result = _paymentService.CreatePaymentAsync(request).Result;

            // Assert
            Console.WriteLine($"Payment URL created: {result.Success}");
            Console.WriteLine($"Payment URL: {result.PaymentUrl}");
            Console.WriteLine($"Message: {result.Message}");

            if (result.Success && !string.IsNullOrEmpty(result.PaymentUrl))
            {
                Console.WriteLine("✅ VNPay URL creation test PASSED");
                
                // Verify URL contains required parameters
                var uri = new Uri(result.PaymentUrl);
                var query = System.Web.HttpUtility.ParseQueryString(uri.Query);
                
                Console.WriteLine($"vnp_TmnCode: {query["vnp_TmnCode"]}");
                Console.WriteLine($"vnp_Amount: {query["vnp_Amount"]}");
                Console.WriteLine($"vnp_TxnRef: {query["vnp_TxnRef"]}");
                Console.WriteLine($"vnp_SecureHash: {query["vnp_SecureHash"]}");
            }
            else
            {
                Console.WriteLine("❌ VNPay URL creation test FAILED");
            }
        }

        public void TestVNPayCallbackProcessing()
        {
            // Arrange - Simulate VNPay callback parameters
            var queryCollection = new QueryCollection(new Dictionary<string, Microsoft.Extensions.Primitives.StringValues>
            {
                {"vnp_Amount", "********"}, // 100,000 VND * 100
                {"vnp_BankCode", "NCB"},
                {"vnp_BankTranNo", "VNP********"},
                {"vnp_CardType", "ATM"},
                {"vnp_OrderInfo", "Test payment"},
                {"vnp_PayDate", "**************"},
                {"vnp_ResponseCode", "00"}, // Success code
                {"vnp_TmnCode", "1BB9SZY8"},
                {"vnp_TransactionNo", "********"},
                {"vnp_TransactionStatus", "00"},
                {"vnp_TxnRef", "12345"},
                {"vnp_SecureHash", "test_hash"} // This would be calculated properly in real scenario
            });

            // Act
            var response = _vnPayHelper.ProcessCallback(queryCollection);

            // Assert
            Console.WriteLine($"Callback processing result: {response.Success}");
            Console.WriteLine($"Response Code: {response.VnPayResponseCode}");
            Console.WriteLine($"Amount: {response.Amount}");
            Console.WriteLine($"Transaction ID: {response.TransactionId}");
            Console.WriteLine($"Is Valid Signature: {response.IsValidSignature}");

            if (response.VnPayResponseCode == "00")
            {
                Console.WriteLine("✅ VNPay callback processing test PASSED");
            }
            else
            {
                Console.WriteLine("❌ VNPay callback processing test FAILED");
            }
        }

        public static void RunTests()
        {
            var test = new VNPayIntegrationTest();
            
            Console.WriteLine("=== VNPay Integration Tests ===");
            Console.WriteLine();
            
            Console.WriteLine("1. Testing VNPay Payment URL Creation:");
            test.TestCreateVNPayPaymentUrl();
            Console.WriteLine();
            
            Console.WriteLine("2. Testing VNPay Callback Processing:");
            test.TestVNPayCallbackProcessing();
            Console.WriteLine();
            
            Console.WriteLine("=== Tests Completed ===");
        }
    }
}
